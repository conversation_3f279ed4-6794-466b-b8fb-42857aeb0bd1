name: Request a cherry-pick
description: As a contributor, you would like to request that a feature be cherry-picked into a release.
title: '[CP] <title>'
labels: ['cp: review']
body:
  - type: markdown
    attributes:
      value: "### The current branches can be found under release-candidate-branch.version for [beta](https://github.com/flutter/flutter/blob/beta/bin/internal/release-candidate-branch.version) and [stable](https://github.com/flutter/flutter/blob/stable/bin/internal/release-candidate-branch.version)"
  - type: input
    id: issue_link
    attributes:
      label: Issue Link
      description: What is the link to the issue this cherry-pick is addressing?
    validations:
      required: true
  - type: dropdown
    id: Target
    attributes:
      label: Target
      description: Should this be cherry-picked to beta or stable?
      options:
        - stable
        - beta
    validations:
      required: true
  - type: input
    id: pr_link
    attributes:
      label: PR Link
      description: >-
        Link to an open PR that cherrypick's this into the target release branch.
        The current branches can be found under release-candidate-branch.version for [beta](https://github.com/flutter/flutter/blob/beta/bin/internal/release-candidate-branch.version) and [stable](https://github.com/flutter/flutter/blob/stable/bin/internal/release-candidate-branch.version)
    validations:
      required: true
  - type: textarea
    id: changelog_description
    attributes:
      label: Changelog Description
      description: >-
        Explain this CP in less than 80 characters that is accessible to most Flutter developers
        See https://github.com/flutter/flutter/blob/main/docs/releases/Hotfix-Documentation-Best-Practices.md for examples
    validations:
      required: true
  - type: input
    id: impacted_users
    attributes:
      label: Impacted Users
      description: Approximately who will hit this issue (ex. all Flutter devs, Windows developers, all end-customers, apps using X framework feature)?
    validations:
      required: true
  - type: textarea
    id: impact_description
    attributes:
      label: Impact Description
      description: What is the impact (ex. visual jank on Samsung phones, app crash, cannot ship an iOS app)? Does it impact development (ex. flutter doctor crashes when Android Studio is installed), or the shipping production app (the app crashes on launch)
    validations:
      required: true
  - type: textarea
    id: workaround
    attributes:
      label: Workaround
      description: Is there a workaround for this issue?
    validations:
      required: true
  - type: dropdown
    id: risk
    attributes:
      label: Risk
      description: What is the risk level of this cherry-pick?
      options:
        - low
        - medium
        - high
    validations:
      required: true
  - type: dropdown
    id: test_coverage
    attributes:
      label: Test Coverage
      description: Are you confident that your fix is well-tested by automated tests?
      options:
        - "yes"
        - "no"
    validations:
      required: true
  - type: textarea
    id: fix_steps
    attributes:
      label: Validation Steps
      description: What are the steps to validate that this fix works?
