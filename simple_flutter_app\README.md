# Simple Flutter App

A simple Flutter application demonstrating basic functionality.

## Features

- Counter functionality
- Material Design UI
- Responsive layout
- Welcome message

## Getting Started

This project is a starting point for a Flutter application.

### Prerequisites

- Flutter SDK installed
- Android Studio or VS Code with Flutter extensions
- Android emulator or physical device

### Running the app

1. Navigate to the project directory:
   ```bash
   cd simple_flutter_app
   ```

2. Get dependencies:
   ```bash
   flutter pub get
   ```

3. Run the app:
   ```bash
   flutter run
   ```

### Building for release

```bash
flutter build apk --release
```

## Project Structure

- `lib/main.dart` - Main application entry point
- `pubspec.yaml` - Project dependencies and configuration
- `android/` - Android-specific configuration
- `ios/` - iOS-specific configuration (when needed)

## What this app does

- Displays a counter that increments when you tap the button
- Shows a welcome message
- Demonstrates basic Flutter widgets and state management
